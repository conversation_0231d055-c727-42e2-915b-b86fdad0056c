{"name": "@yummacss/engine", "version": "0.1.0", "description": "The engine behind Yumma CSS.", "keywords": ["css-framework", "typescript", "yumma<PERSON>s"], "homepage": "https://yummacss.com", "license": "MIT", "author": "<PERSON><PERSON><PERSON>", "files": ["dist"], "type": "module", "exports": {".": "./dist/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/yumma-lib/yumma-css.git"}, "scripts": {"build": "tsdown", "dev": "tsdown --watch", "lint": "biome check --write"}, "packageManager": "pnpm@10.18.0", "dependencies": {"@yummacss/api": "^1.6.2", "globby": "^14.1.0", "zod": "^4.0.17"}, "devDependencies": {"@biomejs/biome": "2.2.5", "@types/node": "^24.2.0", "tsdown": "^0.15.0", "typescript": "^5.9.2"}, "publishConfig": {"access": "public", "provenance": true}}