export const baseCSS = `*,
*::before,
*::after {
  box-sizing: border-box;
  border: 0 solid;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-family: system-ui, sans-serif;
}

body {
  -webkit-font-smoothing: antialiased;
  font-family: inherit;
  line-height: 1.5;
}

canvas,
img,
picture,
svg,
video {
  display: block;
  max-width: 100%;
}

button,
input,
optgroup,
select,
textarea {
  background-color: transparent;
  font-family: inherit;
  padding: .5rem;
}

button:not([class]),
input:not([class]),
optgroup:not([class]),
select:not([class]),
textarea:not([class]) {
  border: 1px solid #bfc2c7;
}

a,
button,
input,
select,
summary,
textarea {
  &:focus {
    outline: 2px solid transparent;
  }
}

textarea:not([rows]) {
  min-height: 10em;
}

button {
  cursor: pointer;
}

button:disabled,
input:disabled,
select:disabled,
textarea:disabled {
  cursor: not-allowed;
  opacity: .5;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  overflow-wrap: break-word;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 1rem;
  font-weight: 600;
  text-wrap: balance;
}

p {
  text-wrap: pretty;
}

b,
strong {
  font-weight: 700;
}

small {
  font-size: 80%;
  line-height: 1.4;
}

pre,
code,
kbd,
samp {
  font-family: monospace;
  font-size: 1em;
}

a {
  color: inherit;
  text-decoration: none;
}

ol,
ul {
  list-style: none;
  padding: 0;
}

th {
  font-size: 1rem;
  font-weight: 600;
}

hr {
  border-top: 1px solid #bfc2c7;
  height: 0;
  margin: 1em 0;
}

details {
  display: block;
}

summary {
  display: list-item;
}`;
